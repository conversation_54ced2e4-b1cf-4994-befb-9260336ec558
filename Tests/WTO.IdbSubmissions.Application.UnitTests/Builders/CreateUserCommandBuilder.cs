using WTO.IdbSubmissions.Application.Features.Users.Commands;

namespace WTO.IdbSubmissions.Application.UnitTests.Builders;

/// <summary>
/// Test data builder for CreateUserCommand
/// </summary>
public class CreateUserCommandBuilder
{
    private string _firstName = "John";
    private string _lastName = "Doe";
    private string _email = "<EMAIL>";
    private int _age = 30;

    /// <summary>
    /// Sets the first name
    /// </summary>
    /// <param name="firstName">First name</param>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithFirstName(string firstName)
    {
        _firstName = firstName;
        return this;
    }

    /// <summary>
    /// Sets the last name
    /// </summary>
    /// <param name="lastName">Last name</param>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithLastName(string lastName)
    {
        _lastName = lastName;
        return this;
    }

    /// <summary>
    /// Sets the email
    /// </summary>
    /// <param name="email">Email address</param>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithEmail(string email)
    {
        _email = email;
        return this;
    }

    /// <summary>
    /// Sets the age
    /// </summary>
    /// <param name="age">Age</param>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithAge(int age)
    {
        _age = age;
        return this;
    }

    /// <summary>
    /// Creates a command for a minor user
    /// </summary>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder AsMinor()
    {
        _age = 16;
        return this;
    }

    /// <summary>
    /// Creates a command for an adult user
    /// </summary>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder AsAdult()
    {
        _age = 25;
        return this;
    }

    /// <summary>
    /// Creates a command with invalid email
    /// </summary>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithInvalidEmail()
    {
        _email = "invalid-email";
        return this;
    }

    /// <summary>
    /// Creates a command with empty first name
    /// </summary>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithEmptyFirstName()
    {
        _firstName = string.Empty;
        return this;
    }

    /// <summary>
    /// Creates a command with whitespace first name
    /// </summary>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithWhitespaceFirstName()
    {
        _firstName = "   ";
        return this;
    }

    /// <summary>
    /// Creates a command with null first name
    /// </summary>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithNullFirstName()
    {
        _firstName = null!;
        return this;
    }

    /// <summary>
    /// Creates a command with empty last name
    /// </summary>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithEmptyLastName()
    {
        _lastName = string.Empty;
        return this;
    }

    /// <summary>
    /// Creates a command with empty email
    /// </summary>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithEmptyEmail()
    {
        _email = string.Empty;
        return this;
    }

    /// <summary>
    /// Creates a command with negative age
    /// </summary>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithNegativeAge()
    {
        _age = -5;
        return this;
    }

    /// <summary>
    /// Creates a command with unrealistic age
    /// </summary>
    /// <returns>Builder instance</returns>
    public CreateUserCommandBuilder WithUnrealisticAge()
    {
        _age = 200;
        return this;
    }

    /// <summary>
    /// Builds the CreateUserCommand
    /// </summary>
    /// <returns>CreateUserCommand instance</returns>
    public CreateUserCommand Build()
    {
        return new CreateUserCommand
        {
            FirstName = _firstName,
            LastName = _lastName,
            Email = _email,
            Age = _age
        };
    }

    /// <summary>
    /// Creates a default CreateUserCommandBuilder instance
    /// </summary>
    /// <returns>CreateUserCommandBuilder instance</returns>
    public static CreateUserCommandBuilder Default() => new();

    /// <summary>
    /// Creates a valid command for an adult user
    /// </summary>
    /// <returns>CreateUserCommandBuilder instance</returns>
    public static CreateUserCommandBuilder ValidAdult() => new CreateUserCommandBuilder().AsAdult();

    /// <summary>
    /// Creates a valid command for a minor user
    /// </summary>
    /// <returns>CreateUserCommandBuilder instance</returns>
    public static CreateUserCommandBuilder ValidMinor() => new CreateUserCommandBuilder().AsMinor();

    /// <summary>
    /// Creates an invalid command with multiple validation errors
    /// </summary>
    /// <returns>CreateUserCommandBuilder instance</returns>
    public static CreateUserCommandBuilder Invalid() => new CreateUserCommandBuilder()
        .WithEmptyFirstName()
        .WithEmptyLastName()
        .WithInvalidEmail()
        .WithNegativeAge();
}
