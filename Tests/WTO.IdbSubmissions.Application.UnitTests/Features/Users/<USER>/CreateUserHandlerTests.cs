using WTO.IdbSubmissions.Application.Features.Users.Commands;
using WTO.IdbSubmissions.Application.UnitTests.Builders;

namespace WTO.IdbSubmissions.Application.UnitTests.Features.Users.Commands;

/// <summary>
/// Unit tests for CreateUserHandler
/// </summary>
[Trait("Category", "Unit")]
[Trait("Layer", "Application")]
public class CreateUserHandlerTests
{
    private readonly CreateUserHandler _handler;

    public CreateUserHandlerTests()
    {
        _handler = new CreateUserHandler();
    }

    #region Success Tests

    [Fact]
    public async Task HandleAsync_WithValidCommand_ShouldReturnSuccessResult()
    {
        // Arrange
        var command = CreateUserCommandBuilder.ValidAdult().Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Id.Should().NotBe(Guid.Empty);
        result.Data.FullName.Should().Be($"{command.FirstName} {command.LastName}");
        result.Data.Email.Should().Be(command.Email.ToLowerInvariant());
        result.Data.IsActive.Should().BeTrue();
        result.Data.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public async Task HandleAsync_WithAdultUser_ShouldSetIsOver18ToTrue()
    {
        // Arrange
        var command = CreateUserCommandBuilder.ValidAdult().WithAge(25).Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data!.IsOver18.Should().BeTrue();
    }

    [Fact]
    public async Task HandleAsync_WithMinorUser_ShouldSetIsOver18ToFalse()
    {
        // Arrange
        var command = CreateUserCommandBuilder.ValidMinor().WithAge(16).Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data!.IsOver18.Should().BeFalse();
    }

    [Fact]
    public async Task HandleAsync_ShouldTrimWhitespaceFromInputs()
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default()
            .WithFirstName("  John  ")
            .WithLastName("  Doe  ")
            .WithEmail("  <EMAIL>  ")
            .Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data!.FullName.Should().Be("John Doe");
        result.Data.Email.Should().Be("<EMAIL>");
    }

    #endregion

    #region Validation Tests

    [Fact]
    public async Task HandleAsync_WithEmptyFirstName_ShouldReturnValidationError()
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default()
            .WithEmptyFirstName()
            .Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain("First name is required");
    }

    [Fact]
    public async Task HandleAsync_WithWhitespaceFirstName_ShouldReturnValidationError()
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default()
            .WithWhitespaceFirstName()
            .Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain("First name is required");
    }

    [Fact]
    public async Task HandleAsync_WithEmptyLastName_ShouldReturnValidationError()
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default()
            .WithEmptyLastName()
            .Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain("Last name is required");
    }

    [Fact]
    public async Task HandleAsync_WithEmptyEmail_ShouldReturnValidationError()
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default()
            .WithEmptyEmail()
            .Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain("Email is required");
    }

    [Fact]
    public async Task HandleAsync_WithInvalidEmail_ShouldReturnValidationError()
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default()
            .WithInvalidEmail()
            .Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain("Email format is invalid");
    }

    [Fact]
    public async Task HandleAsync_WithNegativeAge_ShouldReturnValidationError()
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default()
            .WithNegativeAge()
            .Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain("Age must be a positive number");
    }

    [Fact]
    public async Task HandleAsync_WithUnrealisticAge_ShouldReturnValidationError()
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default()
            .WithUnrealisticAge()
            .Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain("Age must be realistic");
    }

    [Fact]
    public async Task HandleAsync_WithMultipleValidationErrors_ShouldReturnAllErrors()
    {
        // Arrange
        var command = CreateUserCommandBuilder.Invalid().Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().HaveCountGreaterThan(1);
        result.Errors.Should().Contain("First name is required");
        result.Errors.Should().Contain("Last name is required");
        result.Errors.Should().Contain("Email format is invalid");
        result.Errors.Should().Contain("Age must be a positive number");
    }

    #endregion

    #region Edge Cases

    [Theory]
    [InlineData(0)]
    [InlineData(1)]
    [InlineData(17)]
    [InlineData(18)]
    [InlineData(19)]
    [InlineData(65)]
    [InlineData(100)]
    [InlineData(149)]
    public async Task HandleAsync_WithValidAges_ShouldSucceed(int age)
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default()
            .WithAge(age)
            .Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data!.IsOver18.Should().Be(age > 18);
    }

    [Theory]
    [InlineData(150)]
    [InlineData(151)]
    [InlineData(200)]
    public async Task HandleAsync_WithUnrealisticAges_ShouldFail(int age)
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default()
            .WithAge(age)
            .Build();

        // Act
        var result = await _handler.HandleAsync(command);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain("Age must be realistic");
    }

    #endregion

    #region Cancellation Tests

    [Fact]
    public async Task HandleAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var command = CreateUserCommandBuilder.Default().Build();
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _handler.HandleAsync(command, cancellationTokenSource.Token));
    }

    #endregion
}
