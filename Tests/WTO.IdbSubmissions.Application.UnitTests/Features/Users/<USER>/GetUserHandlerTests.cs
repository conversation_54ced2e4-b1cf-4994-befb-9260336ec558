using WTO.IdbSubmissions.Application.Features.Users.Queries;

namespace WTO.IdbSubmissions.Application.UnitTests.Features.Users.Queries;

/// <summary>
/// Unit tests for GetUserHandler
/// </summary>
[Trait("Category", "Unit")]
[Trait("Layer", "Application")]
public class GetUserHandlerTests
{
    private readonly GetUserHandler _handler;

    public GetUserHandlerTests()
    {
        _handler = new GetUserHandler();
    }

    #region Success Tests

    [Fact]
    public async Task HandleAsync_WithValidUserId_ShouldReturnSuccessResult()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetUserQuery { Id = userId };

        // Act
        var result = await _handler.HandleAsync(query);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Id.Should().Be(userId);
        result.Data.FirstName.Should().Be("John");
        result.Data.LastName.Should().Be("Doe");
        result.Data.FullName.Should().Be("<PERSON> Doe");
        result.Data.Email.Should().Be("<EMAIL>");
        result.Data.Age.Should().Be(30);
        result.Data.IsOver18.Should().BeTrue();
        result.Data.IsActive.Should().BeTrue();
        result.Data.CreatedAt.Should().BeCloseTo(DateTime.UtcNow.AddDays(-30), TimeSpan.FromDays(1));
        result.Data.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow.AddDays(-5), TimeSpan.FromDays(1));
    }

    [Fact]
    public async Task HandleAsync_WithValidUserId_ShouldReturnMockUserData()
    {
        // Arrange
        var userId = Guid.Parse("12345678-1234-1234-1234-123456789012");
        var query = new GetUserQuery { Id = userId };

        // Act
        var result = await _handler.HandleAsync(query);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data!.Id.Should().Be(userId);
        result.Data.FirstName.Should().NotBeNullOrEmpty();
        result.Data.LastName.Should().NotBeNullOrEmpty();
        result.Data.Email.Should().NotBeNullOrEmpty();
        result.Data.Age.Should().BeGreaterThan(0);
    }

    #endregion

    #region Validation Tests

    [Fact]
    public async Task HandleAsync_WithEmptyUserId_ShouldReturnValidationError()
    {
        // Arrange
        var query = new GetUserQuery { Id = Guid.Empty };

        // Act
        var result = await _handler.HandleAsync(query);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("User ID is required");
    }

    #endregion

    #region Not Found Tests

    [Fact]
    public async Task HandleAsync_WithUserIdStartingWithZeros_ShouldReturnNotFoundError()
    {
        // Arrange - Using a GUID that starts with zeros to simulate not found
        var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");
        var query = new GetUserQuery { Id = userId };

        // Act
        var result = await _handler.HandleAsync(query);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain($"No user found with ID: {userId}");
    }

    [Theory]
    [InlineData("00000000-0000-0000-0000-000000000001")]
    [InlineData("00000000-1234-5678-9abc-def012345678")]
    [InlineData("00000000-ffff-ffff-ffff-ffffffffffff")]
    public async Task HandleAsync_WithSpecificUserIds_ShouldReturnNotFound(string userIdString)
    {
        // Arrange
        var userId = Guid.Parse(userIdString);
        var query = new GetUserQuery { Id = userId };

        // Act
        var result = await _handler.HandleAsync(query);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain($"No user found with ID: {userId}");
    }

    #endregion

    #region Exception Handling Tests

    [Fact]
    public async Task HandleAsync_WhenExceptionOccurs_ShouldReturnFailureResult()
    {
        // Note: In the current implementation, exceptions are caught and wrapped
        // This test verifies the exception handling behavior
        
        // Arrange
        var query = new GetUserQuery { Id = Guid.NewGuid() };

        // Act
        var result = await _handler.HandleAsync(query);

        // Assert
        // Since the current implementation doesn't throw exceptions for valid GUIDs,
        // this test verifies that the handler completes successfully
        result.Should().NotBeNull();
        // The result should either be success or a controlled failure
        if (!result.IsSuccess)
        {
            result.ErrorMessage.Should().NotBeNullOrEmpty();
        }
    }

    #endregion

    #region Cancellation Tests

    [Fact]
    public async Task HandleAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var query = new GetUserQuery { Id = Guid.NewGuid() };
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _handler.HandleAsync(query, cancellationTokenSource.Token));
    }

    #endregion

    #region Performance Tests

    [Fact]
    public async Task HandleAsync_ShouldCompleteWithinReasonableTime()
    {
        // Arrange
        var query = new GetUserQuery { Id = Guid.NewGuid() };
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = await _handler.HandleAsync(query);

        // Assert
        stopwatch.Stop();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Should complete within 1 second
        result.Should().NotBeNull();
    }

    #endregion

    #region Multiple Calls Tests

    [Fact]
    public async Task HandleAsync_WithSameUserId_ShouldReturnConsistentResults()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetUserQuery { Id = userId };

        // Act
        var result1 = await _handler.HandleAsync(query);
        var result2 = await _handler.HandleAsync(query);

        // Assert
        result1.IsSuccess.Should().Be(result2.IsSuccess);
        if (result1.IsSuccess && result2.IsSuccess)
        {
            result1.Data!.Id.Should().Be(result2.Data!.Id);
            result1.Data.FirstName.Should().Be(result2.Data.FirstName);
            result1.Data.LastName.Should().Be(result2.Data.LastName);
            result1.Data.Email.Should().Be(result2.Data.Email);
        }
    }

    #endregion
}
