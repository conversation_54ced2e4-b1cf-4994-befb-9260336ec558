using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Domain.UnitTests.Builders;

/// <summary>
/// Test data builder for User entity
/// </summary>
public class UserBuilder
{
    private string _firstName = "John";
    private string _lastName = "Doe";
    private string _email = "<EMAIL>";
    private int _age = 30;

    /// <summary>
    /// Sets the first name
    /// </summary>
    /// <param name="firstName">First name</param>
    /// <returns>Builder instance</returns>
    public UserBuilder WithFirstName(string firstName)
    {
        _firstName = firstName;
        return this;
    }

    /// <summary>
    /// Sets the last name
    /// </summary>
    /// <param name="lastName">Last name</param>
    /// <returns>Builder instance</returns>
    public UserBuilder WithLastName(string lastName)
    {
        _lastName = lastName;
        return this;
    }

    /// <summary>
    /// Sets the email
    /// </summary>
    /// <param name="email">Email address</param>
    /// <returns>Builder instance</returns>
    public UserBuilder WithEmail(string email)
    {
        _email = email;
        return this;
    }

    /// <summary>
    /// Sets the age
    /// </summary>
    /// <param name="age">Age</param>
    /// <returns>Builder instance</returns>
    public UserBuilder WithAge(int age)
    {
        _age = age;
        return this;
    }

    /// <summary>
    /// Creates a user with age under 18
    /// </summary>
    /// <returns>Builder instance</returns>
    public UserBuilder AsMinor()
    {
        _age = 16;
        return this;
    }

    /// <summary>
    /// Creates a user with age over 18
    /// </summary>
    /// <returns>Builder instance</returns>
    public UserBuilder AsAdult()
    {
        _age = 25;
        return this;
    }

    /// <summary>
    /// Creates a user with invalid email
    /// </summary>
    /// <returns>Builder instance</returns>
    public UserBuilder WithInvalidEmail()
    {
        _email = "invalid-email";
        return this;
    }

    /// <summary>
    /// Creates a user with empty first name
    /// </summary>
    /// <returns>Builder instance</returns>
    public UserBuilder WithEmptyFirstName()
    {
        _firstName = string.Empty;
        return this;
    }

    /// <summary>
    /// Creates a user with empty last name
    /// </summary>
    /// <returns>Builder instance</returns>
    public UserBuilder WithEmptyLastName()
    {
        _lastName = string.Empty;
        return this;
    }

    /// <summary>
    /// Creates a user with negative age
    /// </summary>
    /// <returns>Builder instance</returns>
    public UserBuilder WithNegativeAge()
    {
        _age = -5;
        return this;
    }

    /// <summary>
    /// Creates a user with unrealistic age
    /// </summary>
    /// <returns>Builder instance</returns>
    public UserBuilder WithUnrealisticAge()
    {
        _age = 200;
        return this;
    }

    /// <summary>
    /// Builds the User entity
    /// </summary>
    /// <returns>User entity</returns>
    public User Build()
    {
        return new User(_firstName, _lastName, _email, _age);
    }

    /// <summary>
    /// Creates a default UserBuilder instance
    /// </summary>
    /// <returns>UserBuilder instance</returns>
    public static UserBuilder Default() => new();

    /// <summary>
    /// Creates a valid adult user
    /// </summary>
    /// <returns>UserBuilder instance</returns>
    public static UserBuilder ValidAdult() => new UserBuilder().AsAdult();

    /// <summary>
    /// Creates a valid minor user
    /// </summary>
    /// <returns>UserBuilder instance</returns>
    public static UserBuilder ValidMinor() => new UserBuilder().AsMinor();
}
