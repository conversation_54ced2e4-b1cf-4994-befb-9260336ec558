using WTO.IdbSubmissions.Domain.Entities;
using WTO.IdbSubmissions.Domain.Events;
using WTO.IdbSubmissions.Domain.UnitTests.Builders;

namespace WTO.IdbSubmissions.Domain.UnitTests.Entities;

/// <summary>
/// Unit tests for User entity
/// </summary>
[Trait("Category", "Unit")]
[Trait("Layer", "Domain")]
public class UserTests
{
    #region Constructor Tests

    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateUser()
    {
        // Arrange
        var firstName = "John";
        var lastName = "Doe";
        var email = "<EMAIL>";
        var age = 30;

        // Act
        var user = new User(firstName, lastName, email, age);

        // Assert
        user.FirstName.Should().Be(firstName);
        user.LastName.Should().Be(lastName);
        user.Email.Should().Be(email);
        user.Age.Should().Be(age);
        user.IsActive.Should().BeTrue();
        user.FullName.Should().Be("John Doe");
        user.IsOver18.Should().BeTrue();
        user.Id.Should().NotBe(Guid.Empty);
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Constructor_WithNullFirstName_ShouldThrowArgumentNullException()
    {
        // Arrange & Act & Assert
        var act = () => new User(null!, "Doe", "<EMAIL>", 30);
        act.Should().Throw<ArgumentNullException>()
            .WithParameterName("firstName");
    }

    [Fact]
    public void Constructor_WithNullLastName_ShouldThrowArgumentNullException()
    {
        // Arrange & Act & Assert
        var act = () => new User("John", null!, "<EMAIL>", 30);
        act.Should().Throw<ArgumentNullException>()
            .WithParameterName("lastName");
    }

    [Fact]
    public void Constructor_WithNullEmail_ShouldThrowArgumentNullException()
    {
        // Arrange & Act & Assert
        var act = () => new User("John", "Doe", null!, 30);
        act.Should().Throw<ArgumentNullException>()
            .WithParameterName("email");
    }

    [Fact]
    public void Constructor_ShouldRaiseUserCreatedEvent()
    {
        // Arrange & Act
        var user = UserBuilder.Default().Build();

        // Assert
        user.DomainEvents.Should().HaveCount(1);
        var domainEvent = user.DomainEvents.First();
        domainEvent.Should().BeOfType<UserCreatedEvent>();
        
        var userCreatedEvent = (UserCreatedEvent)domainEvent;
        userCreatedEvent.UserId.Should().Be(user.Id);
        userCreatedEvent.FullName.Should().Be(user.FullName);
        userCreatedEvent.Email.Should().Be(user.Email);
    }

    #endregion

    #region Property Tests

    [Theory]
    [InlineData(17, false)]
    [InlineData(18, false)]
    [InlineData(19, true)]
    [InlineData(25, true)]
    [InlineData(65, true)]
    public void IsOver18_ShouldReturnCorrectValue(int age, bool expectedResult)
    {
        // Arrange & Act
        var user = UserBuilder.Default().WithAge(age).Build();

        // Assert
        user.IsOver18.Should().Be(expectedResult);
    }

    [Fact]
    public void FullName_ShouldCombineFirstAndLastName()
    {
        // Arrange & Act
        var user = UserBuilder.Default()
            .WithFirstName("Jane")
            .WithLastName("Smith")
            .Build();

        // Assert
        user.FullName.Should().Be("Jane Smith");
    }

    #endregion

    #region UpdateInfo Tests

    [Fact]
    public void UpdateInfo_WithValidParameters_ShouldUpdateUserInfo()
    {
        // Arrange
        var user = UserBuilder.Default().Build();
        user.ClearDomainEvents(); // Clear creation event
        
        var newFirstName = "Jane";
        var newLastName = "Smith";
        var newEmail = "<EMAIL>";
        var newAge = 25;

        // Act
        user.UpdateInfo(newFirstName, newLastName, newEmail, newAge);

        // Assert
        user.FirstName.Should().Be(newFirstName);
        user.LastName.Should().Be(newLastName);
        user.Email.Should().Be(newEmail);
        user.Age.Should().Be(newAge);
        user.FullName.Should().Be("Jane Smith");
    }

    [Fact]
    public void UpdateInfo_ShouldRaiseUserUpdatedEvent()
    {
        // Arrange
        var user = UserBuilder.Default().Build();
        user.ClearDomainEvents(); // Clear creation event

        // Act
        user.UpdateInfo("Jane", "Smith", "<EMAIL>", 25);

        // Assert
        user.DomainEvents.Should().HaveCount(1);
        var domainEvent = user.DomainEvents.First();
        domainEvent.Should().BeOfType<UserUpdatedEvent>();
        
        var userUpdatedEvent = (UserUpdatedEvent)domainEvent;
        userUpdatedEvent.UserId.Should().Be(user.Id);
        userUpdatedEvent.FullName.Should().Be("Jane Smith");
        userUpdatedEvent.Email.Should().Be("<EMAIL>");
    }

    [Fact]
    public void UpdateInfo_WithNullFirstName_ShouldThrowArgumentNullException()
    {
        // Arrange
        var user = UserBuilder.Default().Build();

        // Act & Assert
        var act = () => user.UpdateInfo(null!, "Smith", "<EMAIL>", 25);
        act.Should().Throw<ArgumentNullException>()
            .WithParameterName("firstName");
    }

    #endregion

    #region Deactivate Tests

    [Fact]
    public void Deactivate_ShouldSetIsActiveToFalse()
    {
        // Arrange
        var user = UserBuilder.Default().Build();
        user.ClearDomainEvents(); // Clear creation event

        // Act
        user.Deactivate();

        // Assert
        user.IsActive.Should().BeFalse();
    }

    [Fact]
    public void Deactivate_ShouldRaiseUserDeactivatedEvent()
    {
        // Arrange
        var user = UserBuilder.Default().Build();
        user.ClearDomainEvents(); // Clear creation event

        // Act
        user.Deactivate();

        // Assert
        user.DomainEvents.Should().HaveCount(1);
        var domainEvent = user.DomainEvents.First();
        domainEvent.Should().BeOfType<UserDeactivatedEvent>();
        
        var userDeactivatedEvent = (UserDeactivatedEvent)domainEvent;
        userDeactivatedEvent.UserId.Should().Be(user.Id);
        userDeactivatedEvent.FullName.Should().Be(user.FullName);
    }

    #endregion

    #region Activate Tests

    [Fact]
    public void Activate_ShouldSetIsActiveToTrue()
    {
        // Arrange
        var user = UserBuilder.Default().Build();
        user.Deactivate(); // First deactivate
        user.ClearDomainEvents(); // Clear previous events

        // Act
        user.Activate();

        // Assert
        user.IsActive.Should().BeTrue();
    }

    [Fact]
    public void Activate_ShouldRaiseUserActivatedEvent()
    {
        // Arrange
        var user = UserBuilder.Default().Build();
        user.Deactivate(); // First deactivate
        user.ClearDomainEvents(); // Clear previous events

        // Act
        user.Activate();

        // Assert
        user.DomainEvents.Should().HaveCount(1);
        var domainEvent = user.DomainEvents.First();
        domainEvent.Should().BeOfType<UserActivatedEvent>();
        
        var userActivatedEvent = (UserActivatedEvent)domainEvent;
        userActivatedEvent.UserId.Should().Be(user.Id);
        userActivatedEvent.FullName.Should().Be(user.FullName);
    }

    #endregion
}
