using System.Net;
using WTO.IdbSubmissions.IntegrationTests.WebApplicationFactory;
using WTO.IdbSubmissions.Web.Endpoints.Common;
using WTO.IdbSubmissions.Web.Endpoints.Health;

namespace WTO.IdbSubmissions.IntegrationTests.Endpoints.Health;

/// <summary>
/// Integration tests for Health endpoints
/// </summary>
[Trait("Category", "Integration")]
[Trait("Layer", "API")]
public class HealthEndpointsIntegrationTests : IntegrationTestBase
{
    public HealthEndpointsIntegrationTests(CustomWebApplicationFactory factory) : base(factory)
    {
    }

    #region Basic Health Endpoint Tests

    [Fact]
    public async Task GetHealth_ShouldReturnHealthyStatus()
    {
        // Act
        var response = await Client.GetAsync("/api/health");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();

        var healthResponse = await DeserializeResponseAsync<ApiResponse<HealthResponse>>(response);
        healthResponse.Should().NotBeNull();
        healthResponse!.Success.Should().BeTrue();
        healthResponse.Data.Should().NotBeNull();
        healthResponse.Data!.Status.Should().Be("Healthy");
        healthResponse.Data.Version.Should().Be("1.0.0");
        healthResponse.Data.Environment.Should().NotBeNullOrEmpty();
        healthResponse.Data.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        healthResponse.Message.Should().Be("Service is healthy");
        healthResponse.Errors.Should().BeEmpty();
    }

    [Fact]
    public async Task GetHealth_ShouldReturnCorrectContentType()
    {
        // Act
        var response = await Client.GetAsync("/api/health");

        // Assert
        response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
    }

    [Fact]
    public async Task GetHealth_ShouldAllowAnonymousAccess()
    {
        // Act
        var response = await Client.GetAsync("/api/health");

        // Assert
        response.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized);
        response.StatusCode.Should().NotBe(HttpStatusCode.Forbidden);
    }

    #endregion

    #region Detailed Health Endpoint Tests

    [Fact]
    public async Task GetDetailedHealth_ShouldReturnDetailedHealthStatus()
    {
        // Act
        var response = await Client.GetAsync("/api/health/detailed");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var healthResponse = await DeserializeResponseAsync<ApiResponse<DetailedHealthResponse>>(response);
        healthResponse.Should().NotBeNull();
        healthResponse!.Success.Should().BeTrue();
        healthResponse.Data.Should().NotBeNull();
        
        var data = healthResponse.Data!;
        data.Status.Should().Be("Healthy");
        data.Version.Should().Be("1.0.0");
        data.Environment.Should().NotBeNullOrEmpty();
        data.MachineName.Should().NotBeNullOrEmpty();
        data.ProcessorCount.Should().BeGreaterThan(0);
        data.WorkingSet.Should().BeGreaterThan(0);
        data.TickCount.Should().BeGreaterThan(0);
        data.Dependencies.Should().NotBeNull();
        data.Dependencies.Database.Should().Be("Not Configured");
        data.Dependencies.ExternalServices.Should().Be("Not Configured");
        
        healthResponse.Message.Should().Be("Detailed health status retrieved successfully");
        healthResponse.Errors.Should().BeEmpty();
    }

    [Fact]
    public async Task GetDetailedHealth_ShouldIncludeSystemInformation()
    {
        // Act
        var response = await Client.GetAsync("/api/health/detailed");

        // Assert
        var healthResponse = await DeserializeResponseAsync<ApiResponse<DetailedHealthResponse>>(response);
        var data = healthResponse!.Data!;
        
        data.MachineName.Should().Be(Environment.MachineName);
        data.ProcessorCount.Should().Be(Environment.ProcessorCount);
        data.WorkingSet.Should().Be(Environment.WorkingSet);
    }

    [Fact]
    public async Task GetDetailedHealth_ShouldAllowAnonymousAccess()
    {
        // Act
        var response = await Client.GetAsync("/api/health/detailed");

        // Assert
        response.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized);
        response.StatusCode.Should().NotBe(HttpStatusCode.Forbidden);
    }

    #endregion

    #region Error Handling Tests

    [Fact]
    public async Task GetHealth_WithInvalidRoute_ShouldReturn404()
    {
        // Act
        var response = await Client.GetAsync("/api/health/invalid");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetHealth_WithWrongHttpMethod_ShouldReturn405()
    {
        // Act
        var response = await Client.PostAsync("/api/health", new StringContent(""));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.MethodNotAllowed);
    }

    #endregion

    #region Performance Tests

    [Fact]
    public async Task GetHealth_ShouldRespondQuickly()
    {
        // Arrange
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var response = await Client.GetAsync("/api/health");

        // Assert
        stopwatch.Stop();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Should respond within 1 second
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task GetDetailedHealth_ShouldRespondQuickly()
    {
        // Arrange
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var response = await Client.GetAsync("/api/health/detailed");

        // Assert
        stopwatch.Stop();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Should respond within 1 second
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    #endregion

    #region Concurrent Access Tests

    [Fact]
    public async Task GetHealth_WithConcurrentRequests_ShouldHandleAllRequests()
    {
        // Arrange
        var tasks = new List<Task<HttpResponseMessage>>();
        const int numberOfRequests = 10;

        // Act
        for (int i = 0; i < numberOfRequests; i++)
        {
            tasks.Add(Client.GetAsync("/api/health"));
        }

        var responses = await Task.WhenAll(tasks);

        // Assert
        responses.Should().HaveCount(numberOfRequests);
        responses.Should().OnlyContain(r => r.StatusCode == HttpStatusCode.OK);
    }

    #endregion
}
