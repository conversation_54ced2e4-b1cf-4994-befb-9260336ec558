using System.Net;
using WTO.IdbSubmissions.Application.Features.Users.Commands;
using WTO.IdbSubmissions.Application.Features.Users.Queries;
using WTO.IdbSubmissions.IntegrationTests.WebApplicationFactory;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.IntegrationTests.Endpoints.Users;

/// <summary>
/// Integration tests for User endpoints
/// </summary>
[Trait("Category", "Integration")]
[Trait("Layer", "API")]
public class UserEndpointsIntegrationTests : IntegrationTestBase
{
    public UserEndpointsIntegrationTests(CustomWebApplicationFactory factory) : base(factory)
    {
    }

    #region CreateUser Endpoint Tests

    [Fact]
    public async Task CreateUser_WithValidData_ShouldReturnCreatedUser()
    {
        // Arrange
        var createUserCommand = new CreateUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Age = 30
        };

        // Act
        var response = await Client.PostAsync("/api/users", CreateJsonContent(createUserCommand));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var userResponse = await DeserializeResponseAsync<ApiResponse<CreateUserResponse>>(response);
        userResponse.Should().NotBeNull();
        userResponse!.Success.Should().BeTrue();
        userResponse.Data.Should().NotBeNull();
        
        var data = userResponse.Data!;
        data.Id.Should().NotBe(Guid.Empty);
        data.FullName.Should().Be("John Doe");
        data.Email.Should().Be("<EMAIL>");
        data.IsOver18.Should().BeTrue();
        data.IsActive.Should().BeTrue();
        data.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        
        userResponse.Message.Should().Be("User created successfully");
        userResponse.Errors.Should().BeEmpty();
    }

    [Fact]
    public async Task CreateUser_WithMinorAge_ShouldSetIsOver18ToFalse()
    {
        // Arrange
        var createUserCommand = new CreateUserCommand
        {
            FirstName = "Jane",
            LastName = "Smith",
            Email = "<EMAIL>",
            Age = 16
        };

        // Act
        var response = await Client.PostAsync("/api/users", CreateJsonContent(createUserCommand));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var userResponse = await DeserializeResponseAsync<ApiResponse<CreateUserResponse>>(response);
        userResponse!.Data!.IsOver18.Should().BeFalse();
    }

    [Fact]
    public async Task CreateUser_WithInvalidData_ShouldReturnValidationErrors()
    {
        // Arrange
        var createUserCommand = new CreateUserCommand
        {
            FirstName = "", // Invalid
            LastName = "", // Invalid
            Email = "invalid-email", // Invalid
            Age = -5 // Invalid
        };

        // Act
        var response = await Client.PostAsync("/api/users", CreateJsonContent(createUserCommand));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var errorResponse = await DeserializeResponseAsync<ApiResponse<CreateUserResponse>>(response);
        errorResponse.Should().NotBeNull();
        errorResponse!.Success.Should().BeFalse();
        errorResponse.Errors.Should().NotBeEmpty();
        errorResponse.Errors.Should().Contain("First name is required");
        errorResponse.Errors.Should().Contain("Last name is required");
        errorResponse.Errors.Should().Contain("Email format is invalid");
        errorResponse.Errors.Should().Contain("Age must be a positive number");
    }

    [Fact]
    public async Task CreateUser_WithEmptyBody_ShouldReturnBadRequest()
    {
        // Act
        var response = await Client.PostAsync("/api/users", new StringContent(""));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task CreateUser_WithInvalidJson_ShouldReturnBadRequest()
    {
        // Act
        var response = await Client.PostAsync("/api/users", 
            new StringContent("invalid json", System.Text.Encoding.UTF8, "application/json"));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    #endregion

    #region GetUser Endpoint Tests

    [Fact]
    public async Task GetUser_WithValidId_ShouldReturnUser()
    {
        // Arrange
        var userId = Guid.NewGuid();

        // Act
        var response = await Client.GetAsync($"/api/users/{userId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var userResponse = await DeserializeResponseAsync<ApiResponse<GetUserResponse>>(response);
        userResponse.Should().NotBeNull();
        userResponse!.Success.Should().BeTrue();
        userResponse.Data.Should().NotBeNull();
        
        var data = userResponse.Data!;
        data.Id.Should().Be(userId);
        data.FirstName.Should().Be("John");
        data.LastName.Should().Be("Doe");
        data.FullName.Should().Be("John Doe");
        data.Email.Should().Be("<EMAIL>");
        data.Age.Should().Be(30);
        data.IsOver18.Should().BeTrue();
        data.IsActive.Should().BeTrue();
        
        userResponse.Message.Should().Be("User retrieved successfully");
        userResponse.Errors.Should().BeEmpty();
    }

    [Fact]
    public async Task GetUser_WithUserIdStartingWithZeros_ShouldReturn404()
    {
        // Arrange
        var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

        // Act
        var response = await Client.GetAsync($"/api/users/{userId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        
        var errorResponse = await DeserializeResponseAsync<ApiResponse<GetUserResponse>>(response);
        errorResponse.Should().NotBeNull();
        errorResponse!.Success.Should().BeFalse();
        errorResponse.Errors.Should().Contain($"No user found with ID: {userId}");
    }

    [Fact]
    public async Task GetUser_WithInvalidGuid_ShouldReturnBadRequest()
    {
        // Act
        var response = await Client.GetAsync("/api/users/invalid-guid");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GetUser_WithEmptyGuid_ShouldReturnBadRequest()
    {
        // Act
        var response = await Client.GetAsync($"/api/users/{Guid.Empty}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var errorResponse = await DeserializeResponseAsync<ApiResponse<GetUserResponse>>(response);
        errorResponse!.Success.Should().BeFalse();
        errorResponse.ErrorMessage.Should().Be("User ID is required");
    }

    #endregion

    #region HTTP Method Tests

    [Fact]
    public async Task CreateUser_WithGetMethod_ShouldReturn405()
    {
        // Act
        var response = await Client.GetAsync("/api/users");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.MethodNotAllowed);
    }

    [Fact]
    public async Task GetUser_WithPostMethod_ShouldReturn405()
    {
        // Arrange
        var userId = Guid.NewGuid();

        // Act
        var response = await Client.PostAsync($"/api/users/{userId}", new StringContent(""));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.MethodNotAllowed);
    }

    #endregion

    #region Content Type Tests

    [Fact]
    public async Task CreateUser_ShouldReturnJsonContentType()
    {
        // Arrange
        var createUserCommand = new CreateUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Age = 30
        };

        // Act
        var response = await Client.PostAsync("/api/users", CreateJsonContent(createUserCommand));

        // Assert
        response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
    }

    [Fact]
    public async Task GetUser_ShouldReturnJsonContentType()
    {
        // Arrange
        var userId = Guid.NewGuid();

        // Act
        var response = await Client.GetAsync($"/api/users/{userId}");

        // Assert
        response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
    }

    #endregion

    #region Performance Tests

    [Fact]
    public async Task CreateUser_ShouldRespondQuickly()
    {
        // Arrange
        var createUserCommand = new CreateUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Age = 30
        };
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var response = await Client.PostAsync("/api/users", CreateJsonContent(createUserCommand));

        // Assert
        stopwatch.Stop();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000);
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task GetUser_ShouldRespondQuickly()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var response = await Client.GetAsync($"/api/users/{userId}");

        // Assert
        stopwatch.Stop();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000);
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    #endregion
}
