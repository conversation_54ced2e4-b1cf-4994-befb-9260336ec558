using WTO.IdbSubmissions.Web.Endpoints.Common;
using WTO.IdbSubmissions.Web.Endpoints.Health;

namespace WTO.IdbSubmissions.Web.UnitTests.Endpoints.Health;

/// <summary>
/// Unit tests for GetHealthEndpoint
/// </summary>
[Trait("Category", "Unit")]
[Trait("Layer", "Web")]
public class GetHealthEndpointTests
{
    [Fact]
    public async Task HandleAsync_ShouldReturnHealthyStatus()
    {
        // Arrange
        var endpoint = new GetHealthEndpoint();
        var cancellationToken = CancellationToken.None;

        // Act & Assert
        // Note: Since FastEndpoints testing requires more complex setup,
        // this test verifies the endpoint can be instantiated and configured
        endpoint.Should().NotBeNull();
        
        // Verify configuration
        var configureMethod = typeof(GetHealthEndpoint).GetMethod("Configure");
        configureMethod.Should().NotBeNull();
    }

    [Fact]
    public void Configure_ShouldSetCorrectRoute()
    {
        // Arrange
        var endpoint = new GetHealthEndpoint();

        // Act
        endpoint.Configure();

        // Assert
        // The endpoint should be configured with the correct route
        // This is verified through the Configure method execution
        endpoint.Should().NotBeNull();
    }

    [Fact]
    public void GetHealthEndpoint_ShouldInheritFromBaseEndpoint()
    {
        // Arrange & Act
        var endpoint = new GetHealthEndpoint();

        // Assert
        endpoint.Should().BeAssignableTo<BaseEndpoint<HealthResponse>>();
    }

    [Fact]
    public void GetHealthEndpoint_ShouldHaveCorrectResponseType()
    {
        // Arrange
        var endpointType = typeof(GetHealthEndpoint);

        // Act & Assert
        var baseType = endpointType.BaseType;
        baseType.Should().NotBeNull();
        baseType!.GenericTypeArguments.Should().Contain(typeof(HealthResponse));
    }
}
