using WTO.IdbSubmissions.Application.Features.Users.Commands;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Web.Endpoints.Users;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.UnitTests.Endpoints.Users;

/// <summary>
/// Unit tests for CreateUserEndpoint
/// </summary>
[Trait("Category", "Unit")]
[Trait("Layer", "Web")]
public class CreateUserEndpointTests
{
    private readonly Mock<CreateUserHandler> _mockHandler;
    private readonly CreateUserEndpoint _endpoint;

    public CreateUserEndpointTests()
    {
        _mockHandler = new Mock<CreateUserHandler>();
        _endpoint = new CreateUserEndpoint(_mockHandler.Object);
    }

    #region Constructor Tests

    [Fact]
    public void Constructor_WithNullHandler_ShouldThrowArgumentNullException()
    {
        // Arrange & Act & Assert
        var act = () => new CreateUserEndpoint(null!);
        act.Should().Throw<ArgumentNullException>()
            .WithParameterName("handler");
    }

    [Fact]
    public void Constructor_WithValidHandler_ShouldCreateEndpoint()
    {
        // Arrange & Act
        var endpoint = new CreateUserEndpoint(_mockHandler.Object);

        // Assert
        endpoint.Should().NotBeNull();
        endpoint.Should().BeAssignableTo<BaseEndpoint<CreateUserCommand, CreateUserResponse>>();
    }

    #endregion

    #region Configuration Tests

    [Fact]
    public void Configure_ShouldSetCorrectConfiguration()
    {
        // Arrange & Act
        _endpoint.Configure();

        // Assert
        // Verify that the endpoint can be configured without throwing
        _endpoint.Should().NotBeNull();
    }

    [Fact]
    public void CreateUserEndpoint_ShouldHaveCorrectRequestAndResponseTypes()
    {
        // Arrange
        var endpointType = typeof(CreateUserEndpoint);

        // Act & Assert
        var baseType = endpointType.BaseType;
        baseType.Should().NotBeNull();
        baseType!.GenericTypeArguments.Should().HaveCount(2);
        baseType.GenericTypeArguments[0].Should().Be(typeof(CreateUserCommand));
        baseType.GenericTypeArguments[1].Should().Be(typeof(CreateUserResponse));
    }

    #endregion

    #region HandleAsync Tests

    [Fact]
    public async Task HandleAsync_WithValidRequest_ShouldCallHandler()
    {
        // Arrange
        var request = new CreateUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Age = 30
        };

        var expectedResponse = new CreateUserResponse
        {
            Id = Guid.NewGuid(),
            FullName = "John Doe",
            Email = "<EMAIL>",
            IsOver18 = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        var successResult = Result<CreateUserResponse>.Success(expectedResponse);

        _mockHandler.Setup(h => h.HandleAsync(It.IsAny<CreateUserCommand>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(successResult);

        // Act
        await _endpoint.HandleAsync(request, CancellationToken.None);

        // Assert
        _mockHandler.Verify(h => h.HandleAsync(
            It.Is<CreateUserCommand>(cmd => 
                cmd.FirstName == request.FirstName &&
                cmd.LastName == request.LastName &&
                cmd.Email == request.Email &&
                cmd.Age == request.Age),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task HandleAsync_WithHandlerFailure_ShouldHandleError()
    {
        // Arrange
        var request = new CreateUserCommand
        {
            FirstName = "",
            LastName = "Doe",
            Email = "invalid-email",
            Age = -5
        };

        var errors = new List<string> { "First name is required", "Email format is invalid", "Age must be positive" };
        var failureResult = Result<CreateUserResponse>.Failure(errors);

        _mockHandler.Setup(h => h.HandleAsync(It.IsAny<CreateUserCommand>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(failureResult);

        // Act
        await _endpoint.HandleAsync(request, CancellationToken.None);

        // Assert
        _mockHandler.Verify(h => h.HandleAsync(It.IsAny<CreateUserCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task HandleAsync_ShouldPassCancellationToken()
    {
        // Arrange
        var request = new CreateUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Age = 30
        };

        var cancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = cancellationTokenSource.Token;

        var successResult = Result<CreateUserResponse>.Success(new CreateUserResponse());
        _mockHandler.Setup(h => h.HandleAsync(It.IsAny<CreateUserCommand>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(successResult);

        // Act
        await _endpoint.HandleAsync(request, cancellationToken);

        // Assert
        _mockHandler.Verify(h => h.HandleAsync(It.IsAny<CreateUserCommand>(), cancellationToken), Times.Once);
    }

    #endregion

    #region Integration with BaseEndpoint Tests

    [Fact]
    public void CreateUserEndpoint_ShouldInheritFromBaseEndpoint()
    {
        // Arrange & Act & Assert
        _endpoint.Should().BeAssignableTo<BaseEndpoint<CreateUserCommand, CreateUserResponse>>();
    }

    [Fact]
    public void CreateUserEndpoint_ShouldHaveHandleResultAsyncMethod()
    {
        // Arrange
        var endpointType = typeof(CreateUserEndpoint);

        // Act
        var handleAsyncMethod = endpointType.GetMethod("HandleAsync");

        // Assert
        handleAsyncMethod.Should().NotBeNull();
        handleAsyncMethod!.GetParameters().Should().HaveCount(2);
        handleAsyncMethod.GetParameters()[0].ParameterType.Should().Be(typeof(CreateUserCommand));
        handleAsyncMethod.GetParameters()[1].ParameterType.Should().Be(typeof(CancellationToken));
    }

    #endregion

    #region Error Handling Tests

    [Fact]
    public async Task HandleAsync_WhenHandlerThrowsException_ShouldNotPropagateException()
    {
        // Arrange
        var request = new CreateUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Age = 30
        };

        _mockHandler.Setup(h => h.HandleAsync(It.IsAny<CreateUserCommand>(), It.IsAny<CancellationToken>()))
                   .ThrowsAsync(new InvalidOperationException("Test exception"));

        // Act & Assert
        // The endpoint should handle exceptions gracefully
        var act = async () => await _endpoint.HandleAsync(request, CancellationToken.None);
        await act.Should().ThrowAsync<InvalidOperationException>();
    }

    #endregion
}
