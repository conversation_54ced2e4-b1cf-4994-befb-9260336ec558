namespace WTO.IdbSubmissions.Application.Common.Constants;

/// <summary>
/// Authorization policy constants for the application
/// </summary>
public static class AuthorizationPolicies
{
    /// <summary>
    /// Policy that requires users to have the IDB_MEMBER_USER group claim
    /// </summary>
    public const string IdbMemberPolicy = "IdbMemberPolicy";

    /// <summary>
    /// Policy that requires users to have the IDB_ADMIN_USER group claim
    /// </summary>
    public const string IdbAdminPolicy = "IdbAdminPolicy";

    /// <summary>
    /// Policy that allows access if the user has either IDB_MEMBER_USER OR IDB_ADMIN_USER group claims
    /// </summary>
    public const string IdbUserPolicy = "IdbUserPolicy";
}

/// <summary>
/// Active Directory group claim values
/// </summary>
public static class AdGroupClaims
{
    /// <summary>
    /// IDB Member User group claim value
    /// </summary>
    public const string IdbMemberUser = "IDB_MEMBER_USER";

    /// <summary>
    /// IDB Admin User group claim value
    /// </summary>
    public const string IdbAdminUser = "IDB_ADMIN_USER";

    /// <summary>
    /// Standard claim type for AD groups
    /// </summary>
    public const string GroupsClaimType = "groups";
}
