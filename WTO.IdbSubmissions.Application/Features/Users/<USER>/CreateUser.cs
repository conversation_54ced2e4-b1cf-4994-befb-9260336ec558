using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.Users.Commands;

/// <summary>
/// Command to create a new user
/// </summary>
public class CreateUserCommand
{
    /// <summary>
    /// User's first name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// User's last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// User's email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's age
    /// </summary>
    public int Age { get; set; }
}

/// <summary>
/// Response for CreateUser command
/// </summary>
public class CreateUserResponse
{
    /// <summary>
    /// Created user's ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// User's full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// User's email
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Indicates if the user is over 18
    /// </summary>
    public bool IsOver18 { get; set; }

    /// <summary>
    /// Indicates if the user is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Date when the user was created
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Handler for CreateUser command
/// </summary>
public class CreateUserHandler
{
    /// <summary>
    /// Handles the CreateUser command
    /// </summary>
    /// <param name="command">The command to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the created user response</returns>
    public async Task<Result<CreateUserResponse>> HandleAsync(CreateUserCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the command
            var validationResult = ValidateCommand(command);
            if (!validationResult.IsSuccess)
            {
                return validationResult;
            }

            // Create the user entity
            var user = new User(
                command.FirstName.Trim(),
                command.LastName.Trim(),
                command.Email.Trim().ToLowerInvariant(),
                command.Age);

            // In a real application, you would save to database here
            // For now, we'll simulate the creation
            await Task.Delay(10, cancellationToken); // Simulate async operation

            // Create response
            var response = new CreateUserResponse
            {
                Id = user.Id,
                FullName = user.FullName,
                Email = user.Email,
                IsOver18 = user.IsOver18,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt
            };

            return Result<CreateUserResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<CreateUserResponse>.Failure($"Failed to create user: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the CreateUser command
    /// </summary>
    /// <param name="command">Command to validate</param>
    /// <returns>Validation result</returns>
    private static Result<CreateUserResponse> ValidateCommand(CreateUserCommand command)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(command.FirstName))
            errors.Add("First name is required");

        if (string.IsNullOrWhiteSpace(command.LastName))
            errors.Add("Last name is required");

        if (string.IsNullOrWhiteSpace(command.Email))
            errors.Add("Email is required");
        else if (!IsValidEmail(command.Email))
            errors.Add("Email format is invalid");

        if (command.Age < 0)
            errors.Add("Age must be a positive number");

        if (command.Age > 150)
            errors.Add("Age must be realistic");

        if (errors.Any())
        {
            return Result<CreateUserResponse>.Failure("Validation failed", errors);
        }

        return Result<CreateUserResponse>.Success(new CreateUserResponse());
    }

    /// <summary>
    /// Simple email validation
    /// </summary>
    /// <param name="email">Email to validate</param>
    /// <returns>True if email is valid</returns>
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
