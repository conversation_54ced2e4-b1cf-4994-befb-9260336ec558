using WTO.IdbSubmissions.Domain.Events;

namespace WTO.IdbSubmissions.Domain.Common;

/// <summary>
/// Base class for aggregate roots in the domain
/// Aggregate roots are the only entities that can be directly accessed from outside the aggregate
/// </summary>
public abstract class AggregateRoot : BaseEntity
{
    private readonly List<IDomainEvent> _domainEvents = new();

    /// <summary>
    /// Gets the domain events that have been raised by this aggregate
    /// </summary>
    public new IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Adds a domain event to be published
    /// </summary>
    /// <param name="domainEvent">The domain event to add</param>
    protected new void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    /// <summary>
    /// Removes a specific domain event
    /// </summary>
    /// <param name="domainEvent">The domain event to remove</param>
    protected new void RemoveDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Remove(domainEvent);
    }

    /// <summary>
    /// Clears all domain events
    /// </summary>
    public new void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    /// <summary>
    /// Marks the aggregate as modified and adds a domain event if provided
    /// </summary>
    /// <param name="modifiedBy">User making the modification</param>
    /// <param name="domainEvent">Optional domain event to add</param>
    public override void SetModified(string? modifiedBy = null)
    {
        base.SetModified(modifiedBy);
    }

    /// <summary>
    /// Soft deletes the aggregate and adds a domain event if provided
    /// </summary>
    /// <param name="deletedBy">User performing the deletion</param>
    /// <param name="domainEvent">Optional domain event to add</param>
    public override void SetDeleted(string? deletedBy = null)
    {
        base.SetDeleted(deletedBy);
    }
}
