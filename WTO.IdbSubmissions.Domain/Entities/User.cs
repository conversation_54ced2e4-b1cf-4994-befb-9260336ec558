using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Events;

namespace WTO.IdbSubmissions.Domain.Entities;

/// <summary>
/// User entity representing a system user
/// </summary>
public class User : BaseEntity
{
    /// <summary>
    /// User's first name
    /// </summary>
    public string FirstName { get; private set; } = string.Empty;

    /// <summary>
    /// User's last name
    /// </summary>
    public string LastName { get; private set; } = string.Empty;

    /// <summary>
    /// User's email address
    /// </summary>
    public string Email { get; private set; } = string.Empty;

    /// <summary>
    /// User's age
    /// </summary>
    public int Age { get; private set; }

    /// <summary>
    /// Indicates if the user is active
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// Full name computed property
    /// </summary>
    public string FullName => $"{FirstName} {LastName}";

    /// <summary>
    /// Indicates if the user is over 18
    /// </summary>
    public bool IsOver18 => Age > 18;

    /// <summary>
    /// Private constructor for EF Core
    /// </summary>
    private User() { }

    /// <summary>
    /// Creates a new user
    /// </summary>
    /// <param name="firstName">First name</param>
    /// <param name="lastName">Last name</param>
    /// <param name="email">Email address</param>
    /// <param name="age">Age</param>
    public User(string firstName, string lastName, string email, int age)
    {
        FirstName = firstName ?? throw new ArgumentNullException(nameof(firstName));
        LastName = lastName ?? throw new ArgumentNullException(nameof(lastName));
        Email = email ?? throw new ArgumentNullException(nameof(email));
        Age = age;
        IsActive = true;

        // Raise domain event
        AddDomainEvent(new UserCreatedEvent(Id, FullName, Email));
    }

    /// <summary>
    /// Updates user information
    /// </summary>
    /// <param name="firstName">First name</param>
    /// <param name="lastName">Last name</param>
    /// <param name="email">Email address</param>
    /// <param name="age">Age</param>
    public void UpdateInfo(string firstName, string lastName, string email, int age)
    {
        FirstName = firstName ?? throw new ArgumentNullException(nameof(firstName));
        LastName = lastName ?? throw new ArgumentNullException(nameof(lastName));
        Email = email ?? throw new ArgumentNullException(nameof(email));
        Age = age;

        // Raise domain event
        AddDomainEvent(new UserUpdatedEvent(Id, FullName, Email));
    }

    /// <summary>
    /// Deactivates the user
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
        AddDomainEvent(new UserDeactivatedEvent(Id, FullName));
    }

    /// <summary>
    /// Activates the user
    /// </summary>
    public void Activate()
    {
        IsActive = true;
        AddDomainEvent(new UserActivatedEvent(Id, FullName));
    }
}
