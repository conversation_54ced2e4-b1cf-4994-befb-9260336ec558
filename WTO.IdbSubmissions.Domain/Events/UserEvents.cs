namespace WTO.IdbSubmissions.Domain.Events;

/// <summary>
/// Domain event raised when a user is created
/// </summary>
public class UserCreatedEvent : DomainEvent
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; }

    /// <summary>
    /// User's full name
    /// </summary>
    public string FullName { get; }

    /// <summary>
    /// User's email
    /// </summary>
    public string Email { get; }

    /// <summary>
    /// Initializes a new instance of the UserCreatedEvent
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="fullName">User's full name</param>
    /// <param name="email">User's email</param>
    public UserCreatedEvent(Guid userId, string fullName, string email)
    {
        UserId = userId;
        FullName = fullName;
        Email = email;
    }
}

/// <summary>
/// Domain event raised when a user is updated
/// </summary>
public class UserUpdatedEvent : DomainEvent
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; }

    /// <summary>
    /// User's full name
    /// </summary>
    public string FullName { get; }

    /// <summary>
    /// User's email
    /// </summary>
    public string Email { get; }

    /// <summary>
    /// Initializes a new instance of the UserUpdatedEvent
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="fullName">User's full name</param>
    /// <param name="email">User's email</param>
    public UserUpdatedEvent(Guid userId, string fullName, string email)
    {
        UserId = userId;
        FullName = fullName;
        Email = email;
    }
}

/// <summary>
/// Domain event raised when a user is deactivated
/// </summary>
public class UserDeactivatedEvent : DomainEvent
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; }

    /// <summary>
    /// User's full name
    /// </summary>
    public string FullName { get; }

    /// <summary>
    /// Initializes a new instance of the UserDeactivatedEvent
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="fullName">User's full name</param>
    public UserDeactivatedEvent(Guid userId, string fullName)
    {
        UserId = userId;
        FullName = fullName;
    }
}

/// <summary>
/// Domain event raised when a user is activated
/// </summary>
public class UserActivatedEvent : DomainEvent
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; }

    /// <summary>
    /// User's full name
    /// </summary>
    public string FullName { get; }

    /// <summary>
    /// Initializes a new instance of the UserActivatedEvent
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="fullName">User's full name</param>
    public UserActivatedEvent(Guid userId, string fullName)
    {
        UserId = userId;
        FullName = fullName;
    }
}
