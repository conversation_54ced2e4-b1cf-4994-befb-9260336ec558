using Microsoft.AspNetCore.Authorization;
using WTO.IdbSubmissions.Web.Authorization.Requirements;

namespace WTO.IdbSubmissions.Web.Authorization.Handlers;

/// <summary>
/// Authorization handler for Active Directory group requirements
/// </summary>
public class AdGroupAuthorizationHandler : AuthorizationHandler<AdGroupRequirement>
{
	private readonly ILogger<AdGroupAuthorizationHandler> _logger;

	/// <summary>
	/// Initializes a new instance of the AdGroupAuthorizationHandler
	/// </summary>
	/// <param name="logger">Logger instance</param>
	public AdGroupAuthorizationHandler(ILogger<AdGroupAuthorizationHandler> logger)
	{
		_logger = logger ?? throw new ArgumentNullException(nameof(logger));
	}

	/// <summary>
	/// Handles the authorization requirement
	/// </summary>
	/// <param name="context">Authorization context</param>
	/// <param name="requirement">AD group requirement</param>
	/// <returns>Task representing the authorization operation</returns>
	protected override Task HandleRequirementAsync(
		AuthorizationHandlerContext context,
		AdGroupRequirement          requirement)
	{
		if (context.User?.Identity?.IsAuthenticated != true)
		{
			_logger.LogDebug("User is not authenticated");
			return Task.CompletedTask;
		}

		// Get all group claims for the specified claim type
		var userGroups = context.User.Claims
		                        .Where(c => c.Type == requirement.ClaimType)
		                        .Select(c => c.Value)
		                        .ToList();

		_logger.LogDebug("User has {GroupCount} groups: {Groups}",
			userGroups.Count, string.Join(", ", userGroups));

		// Check if user has any of the required groups
		var hasRequiredGroup = requirement.RequiredGroups.Any(requiredGroup =>
			userGroups.Contains(requiredGroup, StringComparer.OrdinalIgnoreCase));

		if (hasRequiredGroup)
		{
			_logger.LogDebug("User has required group membership. Required: {RequiredGroups}",
				string.Join(", ", requirement.RequiredGroups));
			context.Succeed(requirement);
		}
		else
		{
			_logger.LogWarning("User does not have required group membership. Required: {RequiredGroups}, User has: {UserGroups}",
				string.Join(", ", requirement.RequiredGroups), string.Join(", ", userGroups));
		}

		return Task.CompletedTask;
	}
}