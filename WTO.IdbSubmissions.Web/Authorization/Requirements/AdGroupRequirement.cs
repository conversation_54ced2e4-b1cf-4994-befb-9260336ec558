using Microsoft.AspNetCore.Authorization;

namespace WTO.IdbSubmissions.Web.Authorization.Requirements;

/// <summary>
/// Authorization requirement that checks for specific Active Directory group claims
/// </summary>
public class AdGroupRequirement : IAuthorizationRequirement
{
    /// <summary>
    /// Required AD group claims (any one of these groups will satisfy the requirement)
    /// </summary>
    public IReadOnlyList<string> RequiredGroups { get; }

    /// <summary>
    /// Claim type to check for groups (default: "groups")
    /// </summary>
    public string ClaimType { get; }

    /// <summary>
    /// Initializes a new instance of the AdGroupRequirement
    /// </summary>
    /// <param name="requiredGroups">Required AD group claims</param>
    /// <param name="claimType">Claim type to check (default: "groups")</param>
    public AdGroupRequirement(IEnumerable<string> requiredGroups, string claimType = "groups")
    {
        RequiredGroups = requiredGroups?.ToList().AsReadOnly() 
            ?? throw new ArgumentNullException(nameof(requiredGroups));
        ClaimType = claimType ?? throw new ArgumentNullException(nameof(claimType));

        if (!RequiredGroups.Any())
        {
            throw new ArgumentException("At least one required group must be specified", nameof(requiredGroups));
        }
    }

    /// <summary>
    /// Initializes a new instance of the AdGroupRequirement with a single required group
    /// </summary>
    /// <param name="requiredGroup">Required AD group claim</param>
    /// <param name="claimType">Claim type to check (default: "groups")</param>
    public AdGroupRequirement(string requiredGroup, string claimType = "groups")
        : this(new[] { requiredGroup }, claimType)
    {
    }
}
