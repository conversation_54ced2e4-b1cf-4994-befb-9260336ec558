using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Web.Authorization.Constants;

namespace WTO.IdbSubmissions.Web.Controllers.Api;

/// <summary>
/// Base controller for API endpoints providing common functionality
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Authorize(Policy = AuthorizationPolicies.IdbUserPolicy)]
public abstract class BaseApiController : ControllerBase
{
    /// <summary>
    /// Returns a standardized API response for successful operations
    /// </summary>
    /// <typeparam name="T">Type of the response data</typeparam>
    /// <param name="result">The result to return</param>
    /// <returns>Action result</returns>
    protected ActionResult<T> HandleResult<T>(Result<T> result)
    {
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse<T>
            {
                Success = true,
                Data = result.Data,
                Message = "Operation completed successfully"
            });
        }

        return BadRequest(new ApiResponse<T>
        {
            Success = false,
            Message = result.ErrorMessage ?? "An error occurred",
            Errors = result.Errors.ToList()
        });
    }

    /// <summary>
    /// Returns a standardized API response for operations without return data
    /// </summary>
    /// <param name="result">The result to return</param>
    /// <returns>Action result</returns>
    protected ActionResult HandleResult(Result result)
    {
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse
            {
                Success = true,
                Message = "Operation completed successfully"
            });
        }

        return BadRequest(new ApiResponse
        {
            Success = false,
            Message = result.ErrorMessage ?? "An error occurred",
            Errors = result.Errors.ToList()
        });
    }

    /// <summary>
    /// Returns a not found response
    /// </summary>
    /// <param name="message">Optional custom message</param>
    /// <returns>Not found result</returns>
    protected ActionResult NotFoundResponse(string message = "Resource not found")
    {
        return NotFound(new ApiResponse
        {
            Success = false,
            Message = message
        });
    }

    /// <summary>
    /// Returns an unauthorized response
    /// </summary>
    /// <param name="message">Optional custom message</param>
    /// <returns>Unauthorized result</returns>
    protected ActionResult UnauthorizedResponse(string message = "Unauthorized access")
    {
        return Unauthorized(new ApiResponse
        {
            Success = false,
            Message = message
        });
    }

    /// <summary>
    /// Returns a forbidden response
    /// </summary>
    /// <param name="message">Optional custom message</param>
    /// <returns>Forbidden result</returns>
    protected ActionResult ForbiddenResponse(string message = "Access forbidden")
    {
        return StatusCode(403, new ApiResponse
        {
            Success = false,
            Message = message
        });
    }
}

/// <summary>
/// Standardized API response wrapper
/// </summary>
public class ApiResponse
{
    /// <summary>
    /// Indicates if the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Response message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Collection of error messages
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Timestamp of the response
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Standardized API response wrapper with data
/// </summary>
/// <typeparam name="T">Type of the response data</typeparam>
public class ApiResponse<T> : ApiResponse
{
    /// <summary>
    /// Response data
    /// </summary>
    public T? Data { get; set; }
}
