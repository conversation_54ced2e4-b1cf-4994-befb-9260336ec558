using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTO.IdbSubmissions.Web.Authorization.Constants;

namespace WTO.IdbSubmissions.Web.Controllers;

/// <summary>
/// Example controller demonstrating different authorization policies
/// </summary>
public class ExampleAuthorizationController : Controller
{
    /// <summary>
    /// Public endpoint - no authorization required
    /// </summary>
    [AllowAnonymous]
    public IActionResult Public()
    {
        return Json(new { message = "This is a public endpoint accessible to everyone" });
    }

    /// <summary>
    /// Authenticated endpoint - requires authentication but no specific policy
    /// </summary>
    [Authorize]
    public IActionResult Authenticated()
    {
        return Json(new { 
            message = "This endpoint requires authentication", 
            user = User.Identity?.Name ?? "Unknown" 
        });
    }

    /// <summary>
    /// IDB Member endpoint - requires IDB_MEMBER_USER group
    /// </summary>
    [Authorize(Policy = AuthorizationPolicies.IdbMemberPolicy)]
    public IActionResult MemberOnly()
    {
        return Json(new { 
            message = "This endpoint is accessible only to IDB Members",
            user = User.Identity?.Name ?? "Unknown",
            groups = User.Claims.Where(c => c.Type == AdGroupClaims.GroupsClaimType).Select(c => c.Value)
        });
    }

    /// <summary>
    /// IDB Admin endpoint - requires IDB_ADMIN_USER group
    /// </summary>
    [Authorize(Policy = AuthorizationPolicies.IdbAdminPolicy)]
    public IActionResult AdminOnly()
    {
        return Json(new { 
            message = "This endpoint is accessible only to IDB Admins",
            user = User.Identity?.Name ?? "Unknown",
            groups = User.Claims.Where(c => c.Type == AdGroupClaims.GroupsClaimType).Select(c => c.Value)
        });
    }

    /// <summary>
    /// IDB User endpoint - requires either IDB_MEMBER_USER OR IDB_ADMIN_USER group
    /// </summary>
    [Authorize(Policy = AuthorizationPolicies.IdbUserPolicy)]
    public IActionResult IdbUserAccess()
    {
        return Json(new { 
            message = "This endpoint is accessible to any IDB user (Member or Admin)",
            user = User.Identity?.Name ?? "Unknown",
            groups = User.Claims.Where(c => c.Type == AdGroupClaims.GroupsClaimType).Select(c => c.Value)
        });
    }
}
