using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using WTO.IdbSubmissions.Web.Models;

namespace WTO.IdbSubmissions.Web.Controllers;

/// <summary>
/// Home controller for MVC views
/// Demonstrates Clean Architecture principles in MVC controllers
/// </summary>
public class HomeController : BaseMVCController
{
    private readonly ILogger<HomeController> _logger;

    public HomeController(ILogger<HomeController> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Home page displaying Clean Architecture information
    /// </summary>
    /// <returns>Index view</returns>
    public IActionResult Index()
    {
        _logger.LogInformation("Home page accessed at {Timestamp}", DateTime.UtcNow);

        var model = new HomeViewModel
        {
            Title = "WTO IDB Submissions - Clean Architecture",
            Description = "A .NET 9 Clean Architecture solution ready for CQRS, MediatR, and Entity Framework Core",
            Features = new List<string>
            {
                "Clean Architecture with proper dependency direction",
                "Domain-driven design with aggregate roots and value objects",
                "Application layer with CQRS-ready structure",
                "Infrastructure layer with repository pattern",
                "Persistence layer ready for Entity Framework Core",
                "Web layer supporting both MVC and API endpoints",
                "Domain events with publisher/subscriber pattern",
                "Unit of Work pattern for transaction management",
                "Base classes and interfaces for rapid development"
            },
            ProjectStructure = new Dictionary<string, string>
            {
                { "Core/Domain", "Business entities, value objects, domain events, and core business rules" },
                { "Core/Application", "Application services, DTOs, interfaces, and use case orchestration" },
                { "Infrastructure/Infrastructure", "External service implementations and cross-cutting concerns" },
                { "Infrastructure/Persistence", "Data access, repository implementations, and database context" },
                { "Presentation/Web", "MVC controllers, API controllers, and user interface" }
            }
        };

        return View(model);
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
