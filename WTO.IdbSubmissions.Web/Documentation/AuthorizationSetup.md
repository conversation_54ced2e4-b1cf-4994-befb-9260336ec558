# Authorization Setup Documentation

## Overview

This document describes the authorization policies implemented in the WTO IDB Submissions application using ASP.NET Core's policy-based authorization system with Active Directory group claims.

## Authorization Policies

### 1. IdbMemberPolicy
- **Policy Name**: `IdbMemberPolicy`
- **Required Claim**: `groups` claim containing `IDB_MEMBER_USER`
- **Usage**: For endpoints/controllers that require IDB Member access

### 2. IdbAdminPolicy
- **Policy Name**: `IdbAdminPolicy`
- **Required Claim**: `groups` claim containing `IDB_ADMIN_USER`
- **Usage**: For endpoints/controllers that require IDB Admin access

### 3. IdbUserPolicy
- **Policy Name**: `IdbUserPolicy`
- **Required Claim**: `groups` claim containing either `IDB_MEMBER_USER` OR `IDB_ADMIN_USER`
- **Usage**: For endpoints/controllers that allow access to any IDB user (Member or Admin)

## Configuration

### Program.cs Setup
```csharp
// Add Authentication and Authorization
builder.Services.AddWTOADFSAuthentication(builder.Configuration);
builder.Services.AddIdbAuthorization();

// In middleware pipeline
app.UseAuthentication();
app.UseAuthorization();
```

### Architecture
All authorization-related code is contained within the Web project:
- `WTO.IdbSubmissions.Web/Authorization/Constants/` - Policy and claim constants
- `WTO.IdbSubmissions.Web/Authorization/Requirements/` - Custom authorization requirements
- `WTO.IdbSubmissions.Web/Authorization/Handlers/` - Authorization handlers
- `WTO.IdbSubmissions.Web/Extensions/` - Service registration extensions

### Required Configuration Settings
Add the following to your `appsettings.json` or user secrets:

```json
{
  "AppSettings": {
    "ADFSAuthority": "https://your-adfs-server/adfs",
    "ADFSClientId": "your-client-id",
    "ADFSSecret": "your-client-secret"
  }
}
```

## Usage Examples

### MVC Controllers

#### Using Base Controllers
```csharp
// For IDB Member access
public class MemberController : IdbMemberController
{
    public IActionResult Index() => View();
}

// For IDB Admin access
public class AdminController : IdbAdminController
{
    public IActionResult Index() => View();
}

// For any IDB user access
public class UserController : IdbUserController
{
    public IActionResult Index() => View();
}
```

#### Using Authorize Attributes
```csharp
using WTO.IdbSubmissions.Web.Authorization.Constants;

public class MyController : Controller
{
    [Authorize(Policy = AuthorizationPolicies.IdbMemberPolicy)]
    public IActionResult MemberAction() => View();

    [Authorize(Policy = AuthorizationPolicies.IdbAdminPolicy)]
    public IActionResult AdminAction() => View();

    [Authorize(Policy = AuthorizationPolicies.IdbUserPolicy)]
    public IActionResult UserAction() => View();
}
```

### FastEndpoints

```csharp
using WTO.IdbSubmissions.Web.Authorization.Constants;

public class MyEndpoint : BaseEndpoint<MyResponse>
{
    public override void Configure()
    {
        Get("/my-endpoint");

        // Choose one of these policies:
        Policies(AuthorizationPolicies.IdbMemberPolicy);  // Member only
        Policies(AuthorizationPolicies.IdbAdminPolicy);   // Admin only
        Policies(AuthorizationPolicies.IdbUserPolicy);    // Any IDB user
    }
}
```

## Testing Authorization

### Test Endpoints
The following test endpoints are available to verify authorization:

- `GET /api/member-only` - Requires IDB Member access
- `GET /api/admin-only` - Requires IDB Admin access
- `GET /api/idb-user` - Requires any IDB user access

### MVC Test Actions
- `/ExampleAuthorization/Public` - No authorization required
- `/ExampleAuthorization/Authenticated` - Authentication required
- `/ExampleAuthorization/MemberOnly` - IDB Member required
- `/ExampleAuthorization/AdminOnly` - IDB Admin required
- `/ExampleAuthorization/IdbUserAccess` - Any IDB user required

## Troubleshooting

### Common Issues

1. **403 Forbidden Error**
   - Check that the user has the required group claim
   - Verify the group claim value matches exactly (case-sensitive)
   - Ensure the claim type is "groups"

2. **401 Unauthorized Error**
   - User is not authenticated
   - Check ADFS configuration
   - Verify authentication middleware is properly configured

3. **Policy Not Found Error**
   - Ensure `AddIdbAuthorization()` is called in Program.cs
   - Check that the policy name is spelled correctly

### Debugging Claims
To debug user claims, you can inspect the `User.Claims` collection in your controllers or endpoints:

```csharp
var claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList();
var groups = User.Claims.Where(c => c.Type == "groups").Select(c => c.Value).ToList();
```

## Security Considerations

1. **Group Claim Validation**: The authorization handlers perform case-insensitive comparison of group claims
2. **Authentication Required**: All policies require the user to be authenticated first
3. **Claim Type**: Uses standard "groups" claim type for AD group claims
4. **Logging**: Authorization decisions are logged for auditing purposes
