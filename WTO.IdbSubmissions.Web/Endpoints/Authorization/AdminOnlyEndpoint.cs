using FastEndpoints;
using WTO.IdbSubmissions.Web.Authorization.Constants;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.Authorization;

/// <summary>
/// Response for admin-only endpoint
/// </summary>
public class AdminOnlyResponse
{
    public string Message { get; set; } = string.Empty;
    public string User { get; set; } = string.Empty;
    public List<string> Groups { get; set; } = new();
}

/// <summary>
/// Example endpoint that requires IDB Admin authorization
/// </summary>
public class AdminOnlyEndpoint : BaseEndpoint<AdminOnlyResponse>
{
    public override void Configure()
    {
        Get("/admin-only");
        Policies(AuthorizationPolicies.IdbAdminPolicy);
        Summary(s =>
        {
            s.Summary = "Admin-only endpoint";
            s.Description = "This endpoint is accessible only to users with IDB_ADMIN_USER group claim";
            s.Responses[200] = "Success - user has admin access";
            s.Responses[401] = "Unauthorized - user not authenticated";
            s.Responses[403] = "Forbidden - user does not have required group membership";
        });
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var response = new AdminOnlyResponse
        {
            Message = "This endpoint is accessible only to IDB Admins",
            User = User.Identity?.Name ?? "Unknown",
            Groups = User.Claims
                .Where(c => c.Type == AdGroupClaims.GroupsClaimType)
                .Select(c => c.Value)
                .ToList()
        };

        await SendSuccessAsync(response, "Admin access granted", ct);
    }
}
