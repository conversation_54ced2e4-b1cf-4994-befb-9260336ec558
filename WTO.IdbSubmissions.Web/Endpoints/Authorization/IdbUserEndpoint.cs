using FastEndpoints;
using WTO.IdbSubmissions.Web.Authorization.Constants;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.Authorization;

/// <summary>
/// Response for IDB user endpoint
/// </summary>
public class IdbUserResponse
{
    public string Message { get; set; } = string.Empty;
    public string User { get; set; } = string.Empty;
    public List<string> Groups { get; set; } = new();
    public string AccessLevel { get; set; } = string.Empty;
}

/// <summary>
/// Example endpoint that requires IDB User authorization (Member OR Admin)
/// </summary>
public class IdbUserEndpoint : BaseEndpoint<IdbUserResponse>
{
    public override void Configure()
    {
        Get("/idb-user");
        Policies(AuthorizationPolicies.IdbUserPolicy);
        Summary(s =>
        {
            s.Summary = "IDB user endpoint";
            s.Description = "This endpoint is accessible to users with either IDB_MEMBER_USER or IDB_ADMIN_USER group claims";
            s.Responses[200] = "Success - user has IDB access";
            s.Responses[401] = "Unauthorized - user not authenticated";
            s.Responses[403] = "Forbidden - user does not have required group membership";
        });
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var userGroups = User.Claims
            .Where(c => c.Type == AdGroupClaims.GroupsClaimType)
            .Select(c => c.Value)
            .ToList();

        // Determine access level
        var accessLevel = "Unknown";
        if (userGroups.Contains(AdGroupClaims.IdbAdminUser, StringComparer.OrdinalIgnoreCase))
        {
            accessLevel = "Admin";
        }
        else if (userGroups.Contains(AdGroupClaims.IdbMemberUser, StringComparer.OrdinalIgnoreCase))
        {
            accessLevel = "Member";
        }

        var response = new IdbUserResponse
        {
            Message = "This endpoint is accessible to any IDB user (Member or Admin)",
            User = User.Identity?.Name ?? "Unknown",
            Groups = userGroups,
            AccessLevel = accessLevel
        };

        await SendSuccessAsync(response, "IDB user access granted", ct);
    }
}
