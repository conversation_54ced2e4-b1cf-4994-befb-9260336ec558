using FastEndpoints;
using WTO.IdbSubmissions.Web.Authorization.Constants;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.Authorization;

/// <summary>
/// Response for member-only endpoint
/// </summary>
public class MemberOnlyResponse
{
    public string Message { get; set; } = string.Empty;
    public string User { get; set; } = string.Empty;
    public List<string> Groups { get; set; } = new();
}

/// <summary>
/// Example endpoint that requires IDB Member authorization
/// </summary>
public class MemberOnlyEndpoint : BaseEndpoint<MemberOnlyResponse>
{
    public override void Configure()
    {
        Get("/member-only");
        Policies(AuthorizationPolicies.IdbMemberPolicy);
        Summary(s =>
        {
            s.Summary = "Member-only endpoint";
            s.Description = "This endpoint is accessible only to users with IDB_MEMBER_USER group claim";
            s.Responses[200] = "Success - user has member access";
            s.Responses[401] = "Unauthorized - user not authenticated";
            s.Responses[403] = "Forbidden - user does not have required group membership";
        });
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var response = new MemberOnlyResponse
        {
            Message = "This endpoint is accessible only to IDB Members",
            User = User.Identity?.Name ?? "Unknown",
            Groups = User.Claims
                .Where(c => c.Type == AdGroupClaims.GroupsClaimType)
                .Select(c => c.Value)
                .ToList()
        };

        await SendSuccessAsync(response, "Member access granted", ct);
    }
}
