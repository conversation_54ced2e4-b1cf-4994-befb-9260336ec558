using FastEndpoints;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.Health;

/// <summary>
/// Detailed health check endpoint
/// </summary>
public class GetDetailedHealthEndpoint : BaseEndpoint<DetailedHealthResponse>
{
    public override void Configure()
    {
        Get("/health/detailed");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Detailed health check";
            s.Description = "Returns detailed health status including system information and dependencies";
            s.Responses[200] = "Detailed health status retrieved successfully";
        });
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var detailedHealthInfo = new DetailedHealthResponse
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Environment = System.Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
            MachineName = System.Environment.MachineName,
            ProcessorCount = System.Environment.ProcessorCount,
            WorkingSet = System.Environment.WorkingSet,
            TickCount = System.Environment.TickCount64,
            Dependencies = new DependenciesStatus
            {
                Database = "Not Configured", // Will be updated when EF Core is added
                ExternalServices = "Not Configured"
            }
        };

        await SendSuccessAsync(detailedHealthInfo, "Detailed health status retrieved successfully", ct);
    }
}
