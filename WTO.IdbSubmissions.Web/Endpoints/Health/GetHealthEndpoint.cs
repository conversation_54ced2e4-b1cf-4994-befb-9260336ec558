using FastEndpoints;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.Health;

/// <summary>
/// Basic health check endpoint
/// </summary>
public class GetHealthEndpoint : BaseEndpoint<HealthResponse>
{
    public override void Configure()
    {
        Get("/health");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Basic health check";
            s.Description = "Returns basic health status of the application";
            s.Responses[200] = "Health status retrieved successfully";
        });
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var healthInfo = new HealthResponse
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Environment = System.Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
        };

        await SendSuccessAsync(healthInfo, "Service is healthy", ct);
    }
}
