namespace WTO.IdbSubmissions.Web.Endpoints.Health;

/// <summary>
/// Response DTO for basic health check
/// </summary>
public class HealthResponse
{
    /// <summary>
    /// Health status
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp when health check was performed
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Application version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Environment name
    /// </summary>
    public string Environment { get; set; } = string.Empty;
}

/// <summary>
/// Response DTO for detailed health check
/// </summary>
public class DetailedHealthResponse : HealthResponse
{
    /// <summary>
    /// Machine name
    /// </summary>
    public string MachineName { get; set; } = string.Empty;

    /// <summary>
    /// Number of processors
    /// </summary>
    public int ProcessorCount { get; set; }

    /// <summary>
    /// Working set memory
    /// </summary>
    public long WorkingSet { get; set; }

    /// <summary>
    /// System tick count
    /// </summary>
    public long TickCount { get; set; }

    /// <summary>
    /// Dependencies status
    /// </summary>
    public DependenciesStatus Dependencies { get; set; } = new();
}

/// <summary>
/// Dependencies status information
/// </summary>
public class DependenciesStatus
{
    /// <summary>
    /// Database status
    /// </summary>
    public string Database { get; set; } = "Not Configured";

    /// <summary>
    /// External services status
    /// </summary>
    public string ExternalServices { get; set; } = "Not Configured";
}
