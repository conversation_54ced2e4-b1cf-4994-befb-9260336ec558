using FastEndpoints;
using WTO.IdbSubmissions.Application.Features.Users.Queries;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.Users;

/// <summary>
/// Request DTO for getting a user by ID
/// </summary>
public class GetUserRequest
{
    /// <summary>
    /// User ID to retrieve
    /// </summary>
    [RouteParam]
    public required Guid Id { get; set; }
}

/// <summary>
/// Endpoint for retrieving a user by ID
/// </summary>
public class GetUserEndpoint : BaseEndpoint<GetUserRequest, GetUserResponse>
{
    private readonly GetUserHandler _handler;

    /// <summary>
    /// Initializes a new instance of the GetUserEndpoint
    /// </summary>
    /// <param name="handler">Get user handler</param>
    public GetUserEndpoint(GetUserHandler handler)
    {
        _handler = handler ?? throw new ArgumentNullException(nameof(handler));
    }

    public override void Configure()
    {
        Get("/users/{id}");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get user by ID";
            s.Description = "Retrieves a user by their unique identifier";
            s.Params["id"] = "The unique identifier of the user";
            s.Responses[200] = "User retrieved successfully";
            s.Responses[400] = "Invalid user ID";
            s.Responses[404] = "User not found";
        });
    }

    public override async Task HandleAsync(GetUserRequest req, CancellationToken ct)
    {
        var query = new GetUserQuery { Id = req.Id };
        var result = await _handler.HandleAsync(query, ct);
        
        if (!result.IsSuccess && result.Error.Contains("not found"))
        {
            await SendErrorAsync(result.Error, result.Errors, 404, ct);
        }
        else
        {
            await HandleResultAsync(result, "User retrieved successfully", ct);
        }
    }
}
