using Microsoft.AspNetCore.Authorization;
using WTO.IdbSubmissions.Web.Authorization.Constants;
using WTO.IdbSubmissions.Web.Authorization.Handlers;
using WTO.IdbSubmissions.Web.Authorization.Requirements;

namespace WTO.IdbSubmissions.Web.Extensions;

/// <summary>
/// Extension methods for configuring authorization in the Web layer
/// </summary>
public static class AuthorizationExtensions
{
    /// <summary>
    /// Adds IDB authorization policies and handlers to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddIdbAuthorization(this IServiceCollection services)
    {
        // Register authorization handlers
        services.AddScoped<IAuthorizationHandler, AdGroupAuthorizationHandler>();

        // Configure authorization policies
        services.AddAuthorization(options =>
        {
            // IDB Member Policy - requires IDB_MEMBER_USER group
            options.AddPolicy(AuthorizationPolicies.IdbMemberPolicy, policy =>
            {
                policy.RequireAuthenticatedUser();
                policy.AddRequirements(new AdGroupRequirement(
                    AdGroupClaims.IdbMemberUser, 
                    AdGroupClaims.GroupsClaimType));
            });

            // IDB Admin Policy - requires IDB_ADMIN_USER group
            options.AddPolicy(AuthorizationPolicies.IdbAdminPolicy, policy =>
            {
                policy.RequireAuthenticatedUser();
                policy.AddRequirements(new AdGroupRequirement(
                    AdGroupClaims.IdbAdminUser, 
                    AdGroupClaims.GroupsClaimType));
            });

            // IDB User Policy - requires either IDB_MEMBER_USER OR IDB_ADMIN_USER group
            options.AddPolicy(AuthorizationPolicies.IdbUserPolicy, policy =>
            {
                policy.RequireAuthenticatedUser();
                policy.AddRequirements(new AdGroupRequirement(
                    new[] { AdGroupClaims.IdbMemberUser, AdGroupClaims.IdbAdminUser }, 
                    AdGroupClaims.GroupsClaimType));
            });
        });

        return services;
    }
}
