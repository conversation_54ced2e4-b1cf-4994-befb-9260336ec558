using FastEndpoints;
using FastEndpoints.Swagger;
using WTO.IdbSubmissions.Infrastructure.Services;
using WTO.IdbSubmissions.Web.Extensions;
using WTO.IdbSubmissions.Web.Helpers;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

// Add Authentication and Authorization
builder.Services.AddWTOADFSAuthentication(builder.Configuration);
builder.Services.AddIdbAuthorization();

// Add FastEndpoints
builder.Services.AddFastEndpoints()
    .SwaggerDocument(o =>
    {
        o.DocumentSettings = s =>
        {
            s.Title = "WTO IDB Submissions API";
            s.Version = "v1";
        };
    });

// Add Clean Architecture services
builder.Services.AddDomainEventPublisher();

// Add Application handlers
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.Users.Commands.CreateUserHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.Users.Queries.GetUserHandler>();

// TODO: Add Entity Framework Core when needed
// TODO: Add AutoMapper when needed
// TODO: Add FluentValidation when needed

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

// Configure FastEndpoints
app.UseFastEndpoints(c =>
{
    c.Endpoints.RoutePrefix = "api";
})
.UseSwaggerGen(); // FastEndpoints Swagger integration

app.MapStaticAssets();

// Map MVC routes
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}")
    .WithStaticAssets();

app.Run();

// Make Program class accessible for integration tests
public partial class Program { }
