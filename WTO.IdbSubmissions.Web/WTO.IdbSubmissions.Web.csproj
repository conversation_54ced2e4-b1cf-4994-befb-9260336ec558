<Project Sdk="Microsoft.NET.Sdk.Web">

  <ItemGroup>
    <ProjectReference Include="..\WTO.IdbSubmissions.Application\WTO.IdbSubmissions.Application.csproj" />
    <ProjectReference Include="..\WTO.IdbSubmissions.Infrastructure\WTO.IdbSubmissions.Infrastructure.csproj" />
    <ProjectReference Include="..\WTO.IdbSubmissions.Persistence\WTO.IdbSubmissions.Persistence.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FastEndpoints" Version="6.1.0" />
    <PackageReference Include="FastEndpoints.Swagger" Version="6.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.5" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
    <PackageReference Include="IdentityModel" Version="7.0.0" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>5288bab3-5491-4994-aa3c-032fdbad801e</UserSecretsId>
  </PropertyGroup>

</Project>
