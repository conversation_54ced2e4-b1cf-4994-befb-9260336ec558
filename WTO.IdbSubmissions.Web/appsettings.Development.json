{"Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning", "WTO.IdbSubmissions": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/wto-idb-submissions-dev-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} <s:{SourceContext}> <m:{MachineName}> <p:{ProcessId}> <t:{ThreadId}>{NewLine}{Exception}"}}]}}