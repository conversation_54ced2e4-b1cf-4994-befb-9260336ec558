{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/wto-idb-submissions-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} <s:{SourceContext}> <m:{MachineName}> <p:{ProcessId}> <t:{ThreadId}>{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId", "WithEnvironmentName"], "Properties": {"Application": "WTO.IdbSubmissions"}}, "AllowedHosts": "*"}